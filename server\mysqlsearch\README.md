# MySQL搜索服务 (MySQL Search MCP Server)

这是一个基于MCP (Model Context Protocol) 的MySQL搜索服务，专门用于查询公司信息数据库。

## 功能特性

- 支持根据公司名称进行精确匹配和模糊匹配查询
- 自动降级：先尝试精确匹配，无结果时自动使用模糊匹配
- 支持 `company_name` 和 `alias` 字段的查询
- 返回 `company_id` 和 `company_name` 字段数据
- 提供数据库连接测试功能

## 数据库配置

- **主机**: 114.116.202.190
- **端口**: 5718
- **用户名**: jiachengbin
- **密码**: aiZ3Ooc2aing<ai
- **数据库**: pom_terminal
- **表**: cbirc_company

## 可用工具

### 1. search_company_by_name

根据公司名称查询公司信息。

**参数:**
- `company_name` (string): 公司名称，支持精确匹配和模糊匹配

**返回:**
```json
{
  "success": true,
  "match_type": "exact|fuzzy|none",
  "query": "查询的公司名称",
  "count": 结果数量,
  "data": [
    {
      "company_id": "公司ID",
      "company_name": "公司名称"
    }
  ]
}
```

### 2. test_mysql_connection

测试MySQL数据库连接状态。

**返回:**
```json
{
  "status": "success|failed|warning|error",
  "message": "连接状态描述",
  "database": "数据库名称",
  "table_exists": true|false,
  "table_structure": [...],
  "total_records": 记录总数
}
```

## 安装和运行

### 方式1: 直接运行

1. 安装依赖:
```bash
pip install -r requirements.txt
```

2. 运行服务:
```bash
python mysqlsearch_mcp_server.py
```

服务将在 `http://localhost:8006` 启动。

### 方式2: Docker运行

1. 构建镜像:
```bash
docker build -t mysqlsearch-mcp .
```

2. 运行容器:
```bash
docker run -p 8006:8006 mysqlsearch-mcp
```

### 方式3: Docker Compose

```bash
docker-compose up -d
```

## 测试

运行测试客户端:
```bash
python test_client.py
```

测试将包括:
- 数据库连接测试
- 精确匹配搜索测试
- 模糊匹配搜索测试
- 边界情况测试

## 查询逻辑

1. **精确匹配**: 首先尝试 `company_name = 查询值` 或 `alias = 查询值`
2. **模糊匹配**: 如果精确匹配无结果，则使用 `company_name LIKE %查询值%` 或 `alias LIKE %查询值%`
3. **无结果**: 如果模糊匹配也无结果，返回空数据列表

## 错误处理

服务包含完整的错误处理机制:
- 数据库连接错误
- SQL查询错误
- 参数验证错误
- 未知错误

所有错误都会返回结构化的错误信息，便于调试和处理。

## 日志

服务使用Python标准logging模块记录运行日志，包括:
- 数据库连接状态
- 查询执行情况
- 错误信息
- 匹配结果统计
