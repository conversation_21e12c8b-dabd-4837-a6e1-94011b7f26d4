#!/usr/bin/env python3
"""
Elasticsearch MCP服务测试客户端
用于测试各种搜索功能
"""

import asyncio
import json
import requests
from typing import Dict, Any

class EsSearchClient:
    def __init__(self, base_url: str = "http://localhost:8004"):
        self.base_url = base_url
    
    async def call_tool(self, tool_name: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """调用MCP工具"""
        try:
            # 这里简化处理，实际应该使用MCP协议
            # 为了测试，我们可以直接调用服务的HTTP接口
            print(f"调用工具: {tool_name}")
            print(f"参数: {json.dumps(params, indent=2, ensure_ascii=False)}")
            
            # 模拟调用结果
            return {"status": "success", "tool": tool_name, "params": params}
        except Exception as e:
            return {"error": str(e)}

async def test_health_check(client: EsSearchClient):
    """测试健康检查"""
    print("=== 测试健康检查 ===")
    result = await client.call_tool("health_check", {})
    print(f"结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
    print()

async def test_list_indices(client: EsSearchClient):
    """测试列出索引"""
    print("=== 测试列出索引 ===")
    result = await client.call_tool("list_indices", {"pattern": "*"})
    print(f"结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
    print()

async def test_text_search(client: EsSearchClient):
    """测试文本搜索"""
    print("=== 测试文本搜索 ===")
    params = {
        "index": "news",
        "query": "人工智能 机器学习",
        "fields": ["title", "content"],
        "size": 10,
        "sort_field": "publish_time",
        "sort_order": "desc"
    }
    result = await client.call_tool("search_by_text", params)
    print(f"结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
    print()

async def test_time_range_search(client: EsSearchClient):
    """测试时间范围搜索"""
    print("=== 测试时间范围搜索 ===")
    params = {
        "index": "logs",
        "time_field": "@timestamp",
        "start_time": "2024-01-01",
        "end_time": "2024-01-31",
        "query": "ERROR",
        "size": 20
    }
    result = await client.call_tool("search_by_time_range", params)
    print(f"结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
    print()

async def test_vector_search(client: EsSearchClient):
    """测试向量搜索"""
    print("=== 测试向量搜索 ===")
    # 示例768维向量（实际使用时应该是真实的向量）
    query_vector = [0.1] * 768
    params = {
        "index": "documents",
        "vector_field": "embedding",
        "query_vector": query_vector,
        "k": 10,
        "num_candidates": 100,
        "filter_query": {
            "term": {
                "category": "technology"
            }
        }
    }
    result = await client.call_tool("search_by_vector", params)
    print(f"结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
    print()

async def test_hybrid_search(client: EsSearchClient):
    """测试混合搜索"""
    print("=== 测试混合搜索 ===")
    query_vector = [0.1] * 768
    params = {
        "index": "articles",
        "text_query": "深度学习",
        "vector_field": "content_embedding",
        "query_vector": query_vector,
        "time_field": "created_at",
        "start_time": "2024-01-01",
        "end_time": "2024-12-31",
        "fields": ["title", "summary"],
        "size": 15,
        "text_boost": 1.2,
        "vector_boost": 0.8
    }
    result = await client.call_tool("hybrid_search", params)
    print(f"结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
    print()

async def test_aggregation_search(client: EsSearchClient):
    """测试聚合搜索"""
    print("=== 测试聚合搜索 ===")
    params = {
        "index": "sales",
        "aggs": {
            "sales_over_time": {
                "date_histogram": {
                    "field": "date",
                    "calendar_interval": "month"
                },
                "aggs": {
                    "total_sales": {
                        "sum": {
                            "field": "amount"
                        }
                    }
                }
            },
            "top_products": {
                "terms": {
                    "field": "product.keyword",
                    "size": 10
                }
            }
        },
        "query": {
            "range": {
                "date": {
                    "gte": "2024-01-01",
                    "lte": "2024-12-31"
                }
            }
        },
        "size": 0
    }
    result = await client.call_tool("aggregation_search", params)
    print(f"结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
    print()

async def test_get_index_info(client: EsSearchClient):
    """测试获取索引信息"""
    print("=== 测试获取索引信息 ===")
    params = {
        "index": "news"
    }
    result = await client.call_tool("get_index_info", params)
    print(f"结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
    print()

async def main():
    """主测试函数"""
    print("Elasticsearch MCP服务测试开始...")
    print("=" * 50)
    
    client = EsSearchClient()
    
    # 执行各种测试
    await test_health_check(client)
    await test_list_indices(client)
    await test_text_search(client)
    await test_time_range_search(client)
    await test_vector_search(client)
    await test_hybrid_search(client)
    await test_aggregation_search(client)
    await test_get_index_info(client)
    
    print("=" * 50)
    print("所有测试完成！")
    print("\n注意：这是模拟测试，实际使用时需要：")
    print("1. 确保Elasticsearch服务正在运行")
    print("2. 配置正确的连接参数")
    print("3. 创建相应的索引和数据")
    print("4. 使用真实的MCP客户端连接")

if __name__ == "__main__":
    asyncio.run(main())
