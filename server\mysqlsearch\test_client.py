#!/usr/bin/env python3
"""
MySQL搜索服务测试客户端
"""

import asyncio
import json
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

async def test_mysql_search():
    """测试MySQL搜索服务"""
    
    # 服务器参数
    server_params = StdioServerParameters(
        command="python",
        args=["mysqlsearch_mcp_server.py"]
    )
    
    async with stdio_client(server_params) as (read, write):
        async with ClientSession(read, write) as session:
            # 初始化
            await session.initialize()
            
            print("=== MySQL搜索服务测试 ===\n")
            
            # 测试1: 数据库连接测试
            print("1. 测试数据库连接...")
            try:
                result = await session.call_tool("test_mysql_connection", {})
                print(f"连接测试结果: {json.dumps(result.content[0].text, ensure_ascii=False, indent=2)}")
            except Exception as e:
                print(f"连接测试失败: {e}")
            
            print("\n" + "="*50 + "\n")
            
            # 测试2: 精确匹配搜索
            print("2. 测试精确匹配搜索...")
            test_companies = [
                "中国人寿保险股份有限公司",
                "中国平安",
                "测试公司名称"
            ]
            
            for company in test_companies:
                print(f"\n搜索公司: {company}")
                try:
                    result = await session.call_tool("search_company_by_name", {
                        "company_name": company
                    })
                    result_data = json.loads(result.content[0].text)
                    print(f"搜索结果: {json.dumps(result_data, ensure_ascii=False, indent=2)}")
                except Exception as e:
                    print(f"搜索失败: {e}")
            
            print("\n" + "="*50 + "\n")
            
            # 测试3: 模糊匹配搜索
            print("3. 测试模糊匹配搜索...")
            fuzzy_queries = [
                "人寿",
                "平安",
                "保险"
            ]
            
            for query in fuzzy_queries:
                print(f"\n模糊搜索: {query}")
                try:
                    result = await session.call_tool("search_company_by_name", {
                        "company_name": query
                    })
                    result_data = json.loads(result.content[0].text)
                    print(f"搜索结果: {json.dumps(result_data, ensure_ascii=False, indent=2)}")
                except Exception as e:
                    print(f"搜索失败: {e}")
            
            print("\n" + "="*50 + "\n")
            
            # 测试4: 边界情况测试
            print("4. 测试边界情况...")
            edge_cases = [
                "",  # 空字符串
                "   ",  # 空白字符
                "不存在的公司名称12345"  # 不存在的公司
            ]
            
            for case in edge_cases:
                print(f"\n边界测试: '{case}'")
                try:
                    result = await session.call_tool("search_company_by_name", {
                        "company_name": case
                    })
                    result_data = json.loads(result.content[0].text)
                    print(f"搜索结果: {json.dumps(result_data, ensure_ascii=False, indent=2)}")
                except Exception as e:
                    print(f"搜索失败: {e}")

if __name__ == "__main__":
    asyncio.run(test_mysql_search())
