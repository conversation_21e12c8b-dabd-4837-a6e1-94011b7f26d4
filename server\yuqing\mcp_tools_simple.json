{"mcp_tools": [{"tool_name": "get_sentiment_data", "description": "获取单个实体舆情数据", "input": {"entity_id": "string - 实体ID", "limit": "integer - 返回条数限制，默认10"}, "output": {"sentiment_data": [{"id": "string - 舆情数据ID", "entity_id": "string - 实体ID", "content": "string - 舆情内容", "source": "string - 来源(news/social/regulatory/forum)", "sentiment_score": "float - 情感分数(-1.0到1.0)", "sentiment_label": "string - 情感标签(positive/negative/neutral)", "publish_time": "string - 发布时间(YYYY-MM-DD HH:MM:SS)", "url": "string - 原文链接(可选)", "title": "string - 标题(可选)"}]}, "example": {"input": {"entity_id": "bank_001", "limit": 5}, "output": {"sentiment_data": [{"id": "sentiment_001", "entity_id": "bank_001", "content": "该银行服务质量提升显著", "source": "news", "sentiment_score": 0.75, "sentiment_label": "positive", "publish_time": "2025-07-31 10:00:00", "url": "https://example.com/news/001", "title": "银行服务质量报告"}]}}}, {"tool_name": "get_sentiment_list", "description": "批量获取多实体舆情数据", "input": {"entity_ids": "array[string] - 实体ID列表", "limit_per_entity": "integer - 每个实体返回条数，默认5"}, "output": {"sentiment_data": [{"id": "string - 舆情数据ID", "entity_id": "string - 实体ID", "content": "string - 舆情内容", "source": "string - 来源(news/social/regulatory/forum)", "sentiment_score": "float - 情感分数(-1.0到1.0)", "sentiment_label": "string - 情感标签(positive/negative/neutral)", "publish_time": "string - 发布时间(YYYY-MM-DD HH:MM:SS)", "url": "string - 原文链接(可选)", "title": "string - 标题(可选)"}]}, "example": {"input": {"entity_ids": ["bank_001", "bank_002"], "limit_per_entity": 3}, "output": {"sentiment_data": [{"id": "sentiment_001", "entity_id": "bank_001", "content": "银行业务发展稳健", "source": "news", "sentiment_score": 0.6, "sentiment_label": "positive", "publish_time": "2025-07-31 10:00:00", "url": "https://example.com/news/001", "title": "银行业务报告"}, {"id": "sentiment_002", "entity_id": "bank_002", "content": "该银行被监管处罚", "source": "regulatory", "sentiment_score": -0.7, "sentiment_label": "negative", "publish_time": "2025-07-31 09:45:00", "url": "https://example.com/regulatory/002", "title": "银行处罚通告"}]}}}], "notes": {"返回格式": "支持直接返回数组或包装在sentiment_data字段中", "容错": "MCP不可用时自动使用模拟数据", "异步": "所有工具都是异步调用"}}