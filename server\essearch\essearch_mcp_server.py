from typing import List, Optional, Dict, Any, Union
import os
from datetime import datetime, timedelta
from dotenv import load_dotenv
from mcp.server.fastmcp import FastMCP
from fastapi import FastAPI
import uvicorn
import traceback
import logging
from elasticsearch import Elasticsearch
from elasticsearch.exceptions import ConnectionError, RequestError, NotFoundError
import json
import numpy as np

logger = logging.getLogger(__name__)

# 加载环境变量
load_dotenv()

# Elasticsearch配置
ES_HOST = os.getenv("ES_HOST", "localhost")
ES_PORT = int(os.getenv("ES_PORT", "9200"))
ES_USERNAME = os.getenv("ES_USERNAME", "")
ES_PASSWORD = os.getenv("ES_PASSWORD", "")
ES_USE_SSL = os.getenv("ES_USE_SSL", "false").lower() == "true"
ES_VERIFY_CERTS = os.getenv("ES_VERIFY_CERTS", "false").lower() == "true"

mcp = FastMCP("EsSearchService")

# 初始化Elasticsearch客户端
def get_es_client():
    """获取Elasticsearch客户端"""
    try:
        es = Elasticsearch(
            [{"host": "**************", "port": "9600"}],
            timeout=30,
            max_retries=3,
            retry_on_timeout=True
        )
        
        # 测试连接
        if es.ping():
            logger.info("Elasticsearch连接成功")
            return es
        else:
            logger.error("Elasticsearch连接失败")
            return None
    except Exception as e:
        logger.error(f"创建Elasticsearch客户端失败: {str(e)}")
        return None

@mcp.tool()
async def search_by_text(
    index: str,
    query: str,
    fields: Optional[List[str]] = None,
    size: Optional[int] = 10,
    from_: Optional[int] = 0,
    sort_field: Optional[str] = None,
    sort_order: Optional[str] = "desc"
) -> dict:
    """
    基于文本进行Elasticsearch搜索
    Args:
        index: 索引名称
        query: 搜索查询文本
        fields: 要搜索的字段列表，如果为空则搜索所有字段
        size: 返回结果数量，默认10
        from_: 分页起始位置，默认0
        sort_field: 排序字段
        sort_order: 排序顺序，asc或desc，默认desc
    Returns:
        dict: 搜索结果
    """
    es = get_es_client()
    if not es:
        return {"error": "elasticsearch_connection_failed", "message": "无法连接到Elasticsearch"}
    
    try:
        # 构建查询
        if fields:
            search_query = {
                "multi_match": {
                    "query": query,
                    "fields": fields,
                    "type": "best_fields",
                    "fuzziness": "AUTO"
                }
            }
        else:
            search_query = {
                "query_string": {
                    "query": query,
                    "default_operator": "AND"
                }
            }
        
        body = {
            "query": search_query,
            "size": size,
            "from": from_
        }
        
        # 添加排序，默认倒序
        if sort_field:
            body["sort"] = [{sort_field: {"order": sort_order or "desc"}}]
        
        response = es.search(index=index, body=body)

        hits = []
        for hit in response["hits"]["hits"]:
            source = hit["_source"].copy() if isinstance(hit["_source"], dict) else {}
            # 删除embedding字段
            if "embedding" in source:
                del source["embedding"]
            hits.append({
                "source": source
            })

        return {
            "total": response["hits"]["total"]["value"],
            "max_score": response["hits"]["max_score"],
            "hits": hits
        }
        
    except NotFoundError:
        return {"error": "index_not_found", "message": f"索引 '{index}' 不存在"}
    except RequestError as e:
        logger.error(f"搜索请求错误: {str(e)}")
        return {"error": "search_request_error", "message": f"搜索请求错误: {str(e)}"}
    except Exception as e:
        logger.error(f"搜索过程中出现错误: {str(e)}")
        traceback.print_exc()
        return {"error": "search_error", "message": f"搜索过程中出现错误: {str(e)}"}

@mcp.tool()
async def search_by_time_range(
    index: str,
    time_field: str,
    start_time: str,
    end_time: str,
    query: Optional[str] = None,
    size: Optional[int] = 10,
    from_: Optional[int] = 0,
    sort_order: Optional[str] = "desc"
) -> dict:
    """
    基于时间范围进行Elasticsearch搜索
    Args:
        index: 索引名称
        time_field: 时间字段名称
        start_time: 开始时间，格式：YYYY-MM-DD HH:MM:SS 或 YYYY-MM-DD
        end_time: 结束时间，格式：YYYY-MM-DD HH:MM:SS 或 YYYY-MM-DD
        query: 可选的文本查询
        size: 返回结果数量，默认10
        from_: 分页起始位置，默认0
        sort_order: 时间排序顺序，asc或desc，默认desc
    Returns:
        dict: 搜索结果
    """
    es = get_es_client()
    if not es:
        return {"error": "elasticsearch_connection_failed", "message": "无法连接到Elasticsearch"}
    
    try:
        # 构建时间范围查询
        time_range_query = {
            "range": {
                time_field: {
                    "gte": start_time,
                    "lte": end_time,
                    "format": "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd"
                }
            }
        }
        
        # 如果有文本查询，组合查询
        if query:
            search_query = {
                "bool": {
                    "must": [
                        time_range_query,
                        {
                            "query_string": {
                                "query": query,
                                "default_operator": "AND"
                            }
                        }
                    ]
                }
            }
        else:
            search_query = time_range_query
        
        body = {
            "query": search_query,
            "size": size,
            "from": from_,
            "sort": [{time_field: {"order": sort_order}}]
        }
        
        response = es.search(index=index, body=body)
        
        hits = []
        for hit in response["hits"]["hits"]:
            source = hit["_source"].copy() if isinstance(hit["_source"], dict) else {}
            # 删除embedding字段
            if "embedding" in source:
                del source["embedding"]
            hits.append({
                "source": source
            })

        return {
            "total": response["hits"]["total"]["value"],
            "max_score": response["hits"]["max_score"],
            "time_range": {
                "start": start_time,
                "end": end_time,
                "field": time_field
            },
            "hits": hits
        }
        
    except NotFoundError:
        return {"error": "index_not_found", "message": f"索引 '{index}' 不存在"}
    except RequestError as e:
        logger.error(f"时间范围搜索请求错误: {str(e)}")
        return {"error": "search_request_error", "message": f"时间范围搜索请求错误: {str(e)}"}
    except Exception as e:
        logger.error(f"时间范围搜索过程中出现错误: {str(e)}")
        traceback.print_exc()
        return {"error": "search_error", "message": f"时间范围搜索过程中出现错误: {str(e)}"}




@mcp.tool()
async def get_index_info(index: str) -> dict:
    """
    获取索引信息
    Args:
        index: 索引名称
    Returns:
        dict: 索引信息
    """
    es = get_es_client()
    if not es:
        return {"error": "elasticsearch_connection_failed", "message": "无法连接到Elasticsearch"}

    try:
        # 检查索引是否存在
        if not es.indices.exists(index=index):
            return {"error": "index_not_found", "message": f"索引 '{index}' 不存在"}

        # 获取索引映射
        mapping = es.indices.get_mapping(index=index)

        # 获取索引设置
        settings = es.indices.get_settings(index=index)

        # 获取索引统计信息
        stats = es.indices.stats(index=index)

        return {
            "index": index,
            "exists": True,
            "mapping": mapping[index]["mappings"],
            "settings": settings[index]["settings"],
            "stats": {
                "docs_count": stats["indices"][index]["total"]["docs"]["count"],
                "docs_deleted": stats["indices"][index]["total"]["docs"]["deleted"],
                "store_size": stats["indices"][index]["total"]["store"]["size_in_bytes"],
                "indexing_total": stats["indices"][index]["total"]["indexing"]["index_total"],
                "search_total": stats["indices"][index]["total"]["search"]["query_total"]
            }
        }

    except Exception as e:
        logger.error(f"获取索引信息时出现错误: {str(e)}")
        traceback.print_exc()
        return {"error": "get_index_info_error", "message": f"获取索引信息时出现错误: {str(e)}"}

@mcp.tool()
async def get_question_count(index: str, query: str) -> dict:
    """
    根据用户提供的query字段查询该索引内相关的所有数据的条数
    Args:
        index: 索引名称
        query: 查询字符串
    Returns:
        dict: 查询结果数量信息
    """
    es = get_es_client()
    if not es:
        return {"error": "elasticsearch_connection_failed", "message": "无法连接到Elasticsearch"}

    try:
        # 构建查询
        body = {
            "query": {
                "query_string": {
                    "query": query,
                    "default_operator": "AND"
                }
            }
        }
        response = es.count(index=index, body=body)
        count = response["count"]

        return {
            "index": index,
            "query": query,
            "matched_count": count
        }

    except Exception as e:
        logger.error(f"获取问题数量时出现错误: {str(e)}")
        traceback.print_exc()
        return {"error": "get_question_count_error", "message": f"获取问题数量时出现错误: {str(e)}"}



if __name__ == "__main__":
    app = FastAPI()
    app.mount("/", mcp.sse_app())
    uvicorn.run(app, host="0.0.0.0", port=8005)
