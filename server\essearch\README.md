# Elasticsearch MCP 服务

这是一个基于MCP (Model Context Protocol) 的Elasticsearch搜索服务，提供文本搜索、时间范围搜索、向量搜索和混合搜索功能。

## 功能特性

- **文本搜索**: 支持全文搜索和多字段搜索
- **时间范围搜索**: 基于时间字段进行范围查询
- **向量搜索**: 支持KNN向量相似度搜索
- **混合搜索**: 结合文本、向量和时间范围的综合搜索
- **索引管理**: 查看索引信息和列出所有索引
- **聚合查询**: 支持复杂的聚合分析
- **健康检查**: 监控Elasticsearch连接状态

## 快速开始

### 1. 环境配置

复制环境变量示例文件并配置：

```bash
cp .env.example .env
```

编辑 `.env` 文件，配置你的Elasticsearch连接信息：

```env
ES_HOST=localhost
ES_PORT=9200
ES_USERNAME=your_username
ES_PASSWORD=your_password
ES_USE_SSL=false
ES_VERIFY_CERTS=false
```

### 2. 使用Docker部署

#### 方式一：使用docker-compose（推荐）

```bash
# 构建并启动服务（包含Elasticsearch）
docker-compose up -d

# 仅启动MCP服务（需要外部Elasticsearch）
docker-compose up -d essearch-mcp
```

#### 方式二：使用Docker直接运行

```bash
# 构建镜像
docker build -t essearch:latest .

# 运行容器
docker run -d \
  --name essearch-mcp-server \
  -p 8004:8004 \
  --env-file .env \
  essearch:latest
```

### 3. 本地开发

```bash
# 安装依赖
pip install -r requirements.txt

# 运行服务
python essearch_mcp_server.py
```

服务将在 `http://localhost:8004` 启动。

## API工具说明

### 1. search_by_text
基于文本进行搜索

```python
{
    "index": "my_index",
    "query": "搜索关键词",
    "fields": ["title", "content"],  # 可选
    "size": 10,
    "from_": 0,
    "sort_field": "timestamp",  # 可选
    "sort_order": "desc"
}
```

### 2. search_by_time_range
基于时间范围搜索

```python
{
    "index": "my_index",
    "time_field": "timestamp",
    "start_time": "2024-01-01",
    "end_time": "2024-12-31",
    "query": "可选的文本查询",
    "size": 10
}
```

### 3. search_by_vector
向量相似度搜索

```python
{
    "index": "my_index",
    "vector_field": "embedding",
    "query_vector": [0.1, 0.2, 0.3, ...],  # 向量数组
    "k": 10,
    "num_candidates": 100,
    "filter_query": {"term": {"category": "news"}}  # 可选过滤
}
```

### 4. hybrid_search
混合搜索（文本+向量+时间）

```python
{
    "index": "my_index",
    "text_query": "搜索文本",
    "vector_field": "embedding",
    "query_vector": [0.1, 0.2, 0.3, ...],
    "time_field": "timestamp",
    "start_time": "2024-01-01",
    "end_time": "2024-12-31",
    "text_boost": 1.0,
    "vector_boost": 1.0
}
```

### 5. get_index_info
获取索引详细信息

```python
{
    "index": "my_index"
}
```

### 6. list_indices
列出所有索引

```python
{
    "pattern": "*"  # 可选，默认列出所有索引
}
```

### 7. aggregation_search
聚合查询

```python
{
    "index": "my_index",
    "aggs": {
        "date_histogram": {
            "field": "timestamp",
            "calendar_interval": "day"
        }
    },
    "query": {"match_all": {}},  # 可选
    "size": 0
}
```

### 8. health_check
健康检查

```python
{}  # 无需参数
```

## 使用示例

### 文本搜索示例

```python
# 在新闻索引中搜索包含"人工智能"的文章
result = await search_by_text(
    index="news",
    query="人工智能",
    fields=["title", "content"],
    size=20
)
```

### 时间范围搜索示例

```python
# 搜索最近7天的数据
result = await search_by_time_range(
    index="logs",
    time_field="@timestamp",
    start_time="2024-01-01",
    end_time="2024-01-07",
    query="ERROR"
)
```

### 向量搜索示例

```python
# 基于文档向量进行相似度搜索
result = await search_by_vector(
    index="documents",
    vector_field="doc_embedding",
    query_vector=[0.1, 0.2, 0.3, ...],  # 768维向量
    k=10
)
```

## 注意事项

1. **向量搜索要求**: 确保你的Elasticsearch版本支持KNN搜索（8.0+）
2. **索引映射**: 向量字段需要正确的映射配置
3. **性能优化**: 大规模数据建议调整`num_candidates`参数
4. **安全配置**: 生产环境建议启用SSL和身份验证

## 故障排除

### 连接问题
- 检查Elasticsearch是否正常运行
- 验证网络连接和端口配置
- 确认认证信息正确

### 搜索问题
- 确认索引存在且有数据
- 检查字段映射是否正确
- 验证查询语法

### 向量搜索问题
- 确保Elasticsearch版本支持KNN
- 检查向量字段映射配置
- 验证向量维度匹配

## 开发和贡献

欢迎提交Issue和Pull Request来改进这个服务。

## 许可证

MIT License
